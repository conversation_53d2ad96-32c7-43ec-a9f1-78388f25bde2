<style>
    #export-statistics {
        font-size: 14px;
    }
</style>

<!-- Export Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div class="flex items-center">
            <a href="{{ back_url }}" data-readdy="true" class="mr-3 text-gray-500 hover:text-primary">
                <div class="w-8 h-8 flex items-center justify-center">
                    <i class="ri-arrow-left-line ri-lg"></i>
                </div>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Експорт на продукти</h1>
                <p class="text-gray-500 mt-1">Експортиране на продукти в CSV, XML или XLSX формати</p>
            </div>
        </div>
        <div class="mt-4 md:mt-0 flex flex-col sm:flex-row gap-2">
            <a href="{{ back_url }}" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 transition-colors whitespace-nowrap flex items-center !rounded-button">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-arrow-left-line"></i>
                </div>
                <span>Назад към продукти</span>
            </a>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto overflow-x-hidden bg-gray-50" style="padding-bottom: 200px">


<!-- Main Content -->
<div class="p-6">
    <!-- Status Messages -->
    <div id="alert-container" class="mb-6"></div>

    <!-- Supported Formats Info -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
        <div class="flex items-start">
            <div class="w-6 h-6 flex items-center justify-center mr-3 mt-0.5">
                <i class="ri-information-line text-blue-600"></i>
            </div>
            <div class="flex-1">
                <h3 class="text-lg font-semibold text-blue-900 mb-2">Поддържани формати за експорт</h3>

                {% if supported_formats|length == 0 %}
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                        <div class="flex items-center">
                            <i class="ri-error-warning-line text-red-600 mr-2"></i>
                            <div>
                                <h4 class="text-sm font-semibold text-red-800">Няма налични формати за експорт</h4>
                                <p class="text-xs text-red-700 mt-1">
                                    Моля, свържете се с администратора за инсталиране на необходимите библиотеки.
                                </p>
                            </div>
                        </div>
                    </div>
                {% endif %}

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    {% for format_key, format_info in format_requirements %}
                        <div class="bg-white rounded-lg p-4 border border-blue-100 {% if not format_info.available %}opacity-50{% endif %} relative group">
                            <div class="flex items-center mb-2">
                                <i class="{{ format_info.icon }} {% if format_info.available %}text-green-600{% else %}text-gray-400{% endif %} mr-2"></i>
                                <span class="font-medium {% if format_info.available %}text-gray-800{% else %}text-gray-500{% endif %}">{{ format_info.name }}</span>
                                {% if format_info.available %}
                                    <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">Наличен</span>
                                {% else %}
                                    <span class="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">Не е наличен</span>
                                    <i class="ri-information-line text-gray-400 ml-1 cursor-help"></i>
                                {% endif %}
                            </div>
                            <p class="text-sm {% if format_info.available %}text-gray-600{% else %}text-gray-500{% endif %}">{{ format_info.description }}</p>
                            <p class="text-xs {% if format_info.available %}text-gray-500{% else %}text-red-500{% endif %} mt-1">
                                {% if format_info.available %}
                                    {% if format_key == 'csv' %}Препоръчителен за големи обеми данни{% endif %}
                                    {% if format_key == 'xml' %}Подходящ за структурирани данни{% endif %}
                                    {% if format_key == 'xlsx' %}Удобен за редактиране в Excel{% endif %}
                                {% else %}
                                    <i class="ri-close-circle-line mr-1"></i>{{ format_info.reason }}
                                {% endif %}
                            </p>

                            {% if not format_info.available %}
                                <!-- Tooltip за недостъпни формати -->
                                <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 whitespace-nowrap">
                                    <div class="text-center">
                                        <div class="font-medium">Защо не е наличен?</div>
                                        <div class="mt-1">{{ format_info.reason }}</div>
                                    </div>
                                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
                <div class="flex items-center justify-between">
                    <p class="text-sm text-blue-800">
                        <span class="font-medium">Експортирайте продукти</span> в удобен за вас формат
                    </p>
                    <p class="text-sm text-gray-600">
                        <span class="font-medium text-green-600">{{ supported_formats|length }}</span> от {{ format_requirements|length }} формата налични
                        {% if unavailable_formats|length > 0 %}
                            <span class="text-red-500 ml-2">({{ unavailable_formats|length }} недостъпни)</span>
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Form -->
    {% if supported_formats|length > 0 %}
    <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
        <div class="border-b border-gray-200 px-6 py-4">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <div class="w-6 h-6 flex items-center justify-center mr-2">
                    <i class="ri-download-line text-primary"></i>
                </div>
                Експорт на продукти
            </h3>
        </div>
        <div class="p-6">
            <form id="export-form" class="space-y-6">
                <input type="hidden" name="user_token" value="{{ user_token }}">
                
                <!-- Format Selection -->
                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-3">
                            <span class="text-red-500">*</span> Изберете формат за експорт
                        </label>
                        <div class="space-y-2">
                            {% for format in supported_formats %}
                                {% set format_info = format_requirements[format] %}
                                <div class="flex items-center">
                                    <input type="radio"
                                           id="format_{{ format }}"
                                           name="export_format"
                                           value="{{ format }}"
                                           {% if loop.first %}checked{% endif %}
                                           class="w-4 h-4 text-primary border-gray-300 focus:ring-primary focus:ring-2">
                                    <label for="format_{{ format }}" class="ml-2 text-sm text-gray-700 flex items-center">
                                        <i class="{{ format_info.icon }} mr-2"></i>
                                        <span class="font-medium">{{ format_info.name }}</span>
                                        <span class="ml-2 text-gray-500">({{ format_info.description }})</span>
                                    </label>
                                </div>
                            {% endfor %}
                        </div>
                        <div id="format-info" class="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded"></div>
                    </div>

                    <!-- Basic Export Options -->
                    <div class="border-t border-gray-200 pt-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">Основни опции за експорт</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <input type="checkbox"
                                           id="basic_product_id"
                                           name="basic_product_id"
                                           value="1"
                                           checked
                                           disabled
                                           class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2">
                                    <label for="basic_product_id" class="ml-2 text-sm text-gray-500">
                                        ID на продукт (задължително)
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox"
                                           id="basic_product_name"
                                           name="basic_product_name"
                                           value="1"
                                           checked
                                           disabled
                                           class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2">
                                    <label for="basic_product_name" class="ml-2 text-sm text-gray-500">
                                        Име на продукта (задължително)
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox"
                                           id="basic_model"
                                           name="basic_model"
                                           value="1"
                                           checked
                                           disabled
                                           class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2">
                                    <label for="basic_model" class="ml-2 text-sm text-gray-500">
                                        Модел (задължително)
                                    </label>
                                </div>
                                 <div class="flex items-center">
                                    <input type="checkbox"
                                           id="include_categories"
                                           name="include_categories"
                                           value="1"
                                           class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2">
                                    <label for="include_categories" class="ml-2 text-sm text-gray-700">
                                        Включване на категории
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox"
                                           id="include_attributes"
                                           name="include_attributes"
                                           value="1"
                                           class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2">
                                    <label for="include_attributes" class="ml-2 text-sm text-gray-700">
                                        Включване на атрибути
                                    </label>
                                </div>
                            </div>
                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <input type="checkbox"
                                           id="include_quantity"
                                           name="include_quantity"
                                           value="1"
                                           class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2">
                                    <label for="include_quantity" class="ml-2 text-sm text-gray-700">
                                        Включване на количество
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox"
                                           id="include_price"
                                           name="include_price"
                                           value="1"
                                           class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2">
                                    <label for="include_price" class="ml-2 text-sm text-gray-700">
                                        Включване на цена
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox"
                                           id="include_promo_price"
                                           name="include_promo_price"
                                           value="1"
                                           class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2">
                                    <label for="include_promo_price" class="ml-2 text-sm text-gray-700">
                                        Включване на промо цена
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox"
                                           id="include_descriptions"
                                           name="include_descriptions"
                                           value="1"
                                           class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2">
                                    <label for="include_descriptions" class="ml-2 text-sm text-gray-700">
                                        Включване на описания
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox"
                                           id="include_options"
                                           name="include_options"
                                           value="1"
                                           class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2">
                                    <label for="include_options" class="ml-2 text-sm text-gray-700">
                                        Включване на опции
                                    </label>
                                </div>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-3">
                            Основните полета са винаги включени. Изберете допълнителни данни за включване в експорта.
                        </p>
                    </div>

                    <!-- Advanced Field Selection -->
                    <div class="border-t border-gray-200 pt-6">
                        <div class="flex items-center justify-between mb-4">
                            <h5 class="text-sm font-medium text-gray-700">Детайлен избор на полета</h5>
                            <button type="button" id="toggle-field-selector" class="text-xs text-primary hover:text-primary-dark flex items-center">
                                <span id="toggle-field-text">Покажи опции</span>
                                <i id="toggle-field-icon" class="ri-arrow-down-s-line ml-1"></i>
                            </button>
                        </div>

                        <div id="field-selector-container" class="hidden">
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex gap-2 mb-4">
                                    <button type="button" id="select-all-fields" class="px-3 py-1 text-xs bg-primary text-white rounded hover:bg-primary-dark">
                                        Избери всички
                                    </button>
                                    <button type="button" id="select-none-fields" class="px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600">
                                        Премахни всички
                                    </button>
                                </div>

                                <div class="space-y-4">
                                    {% if grouped_fields is defined and group_labels is defined %}
                                        {% for group_key, group_fields in grouped_fields %}
                                            {% if group_fields|length > 0 %}
                                                <div class="border border-gray-200 rounded-lg p-3 bg-white">
                                                    <h6 class="font-medium text-gray-800 mb-2 flex items-center">
                                                        <input type="checkbox" id="toggle-{{ group_key }}" class="mr-2 group-toggle">
                                                        {{ group_labels[group_key] ?? group_key }}
                                                    </h6>
                                                    <div class="grid grid-cols-2 gap-2 text-sm" data-group="{{ group_key }}">
                                                        {% for field, label in group_fields %}
                                                            {% if field not in ['product_id', 'name', 'model', 'quantity', 'price', 'promo_price', 'description', 'options', 'categories', 'attributes'] %}
                                                                <div class="flex items-center">
                                                                    <input type="checkbox"
                                                                           id="field_{{ field }}"
                                                                           name="selected_fields[]"
                                                                           value="{{ field }}"
                                                                           class="w-3 h-3 text-primary border-gray-300 rounded focus:ring-primary focus:ring-1 field-checkbox"
                                                                           data-group="{{ group_key }}">
                                                                    <label for="field_{{ field }}" class="ml-1 text-gray-700 text-xs">
                                                                        {{ label }}
                                                                    </label>
                                                                </div>
                                                            {% endif %}
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        <!-- Fallback to old structure -->
                                        <!-- Basic Fields -->
                                        <div class="border border-gray-200 rounded-lg p-3 bg-white">
                                            <h6 class="font-medium text-gray-800 mb-2 flex items-center">
                                                <input type="checkbox" id="toggle-basic" class="mr-2 group-toggle">
                                                Основни полета
                                            </h6>
                                            <div class="grid grid-cols-2 gap-2 text-sm" data-group="basic">
                                                {% if available_fields.basic is defined %}
                                                    {% for field, label in available_fields.basic %}
                                                        {% if field not in ['product_id', 'name', 'model', 'quantity', 'price', 'promo_price'] %}
                                                            <div class="flex items-center">
                                                                <input type="checkbox"
                                                                       id="field_{{ field }}"
                                                                       name="selected_fields[]"
                                                                       value="{{ field }}"
                                                                       class="w-3 h-3 text-primary border-gray-300 rounded focus:ring-primary focus:ring-1 field-checkbox"
                                                                       data-group="basic">
                                                                <label for="field_{{ field }}" class="ml-1 text-gray-700 text-xs">
                                                                    {{ label }}
                                                                </label>
                                                            </div>
                                                        {% endif %}
                                                    {% endfor %}
                                                {% endif %}
                                            </div>
                                        </div>

                                        <!-- Multilingual Fields -->
                                        <div class="border border-gray-200 rounded-lg p-3 bg-white">
                                            <h6 class="font-medium text-gray-800 mb-2 flex items-center">
                                                <input type="checkbox" id="toggle-multilingual" class="mr-2 group-toggle">
                                                Многоезични полета
                                            </h6>
                                            <div class="grid grid-cols-2 gap-2 text-sm" data-group="multilingual">
                                                {% if available_fields.multilingual is defined %}
                                                    {% for field, label in available_fields.multilingual %}
                                                        {% if field not in ['name', 'description'] %}
                                                            <div class="flex items-center">
                                                                <input type="checkbox"
                                                                       id="field_{{ field }}"
                                                                       name="selected_fields[]"
                                                                       value="{{ field }}"
                                                                       class="w-3 h-3 text-primary border-gray-300 rounded focus:ring-primary focus:ring-1 field-checkbox"
                                                                       data-group="multilingual">
                                                                <label for="field_{{ field }}" class="ml-1 text-gray-700 text-xs">
                                                                    {{ label }}
                                                                </label>
                                                            </div>
                                                        {% endif %}
                                                    {% endfor %}
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>

                                <p class="text-xs text-gray-500 mt-3">
                                    <strong>Забележка:</strong> Основните полета (ID, име, модел) винаги са включени. Полетата от основните опции по-горе (количество, цена, промо цена, описания, категории, опции, атрибути) се управляват от съответните чекбоксове и не се показват тук за да се избегне дублиране.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Product Selection -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-lg font-medium text-gray-800 mb-4">Избор на продукти за експорт</h4>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Category Selection -->
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Избор по категории
                                </label>
                                <div class="relative">
                                    <input type="text"
                                           id="category-autocomplete"
                                           placeholder="Търсете категория..."
                                           class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
                                    <div id="category-results" class="absolute z-10 w-full bg-white border border-gray-300 rounded-button mt-1 max-h-60 overflow-y-auto hidden"></div>
                                </div>
                                <div class="flex items-center justify-between mt-2">
                                    <p class="text-xs text-gray-500">
                                        Избрани категории: <span id="category-count" class="font-medium">0</span>
                                    </p>
                                    <div class="flex gap-2">
                                        <button type="button" id="clear-categories" class="text-xs text-red-600 hover:text-red-800">Изчисти всички</button>
                                    </div>
                                </div>
                                <div id="selected-categories" class="mt-2 space-y-1"></div>
                            </div>
                        </div>

                        <!-- Product Selection -->
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Избор на конкретни продукти
                                </label>
                                <div class="relative">
                                    <input type="text"
                                           id="product-autocomplete"
                                           placeholder="Търсете продукт..."
                                           class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
                                    <div id="product-results" class="absolute z-10 w-full bg-white border border-gray-300 rounded-button mt-1 max-h-60 overflow-y-auto hidden"></div>
                                </div>
                                <div class="flex items-center justify-between mt-2">
                                    <p class="text-xs text-gray-500">
                                        Избрани продукти: <span id="product-count" class="font-medium">0</span>
                                    </p>
                                    <div class="flex gap-2">
                                        <button type="button" id="clear-products" class="text-xs text-red-600 hover:text-red-800">Изчисти всички</button>
                                    </div>
                                </div>
                                <div id="selected-products" class="mt-2 space-y-1"></div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div class="flex items-start">
                            <i class="ri-information-line text-yellow-600 mr-2 mt-0.5"></i>
                            <div class="text-sm text-yellow-800">
                                <p class="font-medium">Забележка:</p>
                                <p>Ако не изберете категории или продукти, ще бъдат експортирани всички активни продукти.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-start border-t border-gray-200 pt-6">
                    <button type="submit" id="export-submit-btn" class="px-6 py-3 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors flex items-center !rounded-button">
                        <div class="w-5 h-5 flex items-center justify-center mr-2">
                            <i class="ri-download-line"></i>
                        </div>
                        <span>Започни експорт</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Export Statistics -->
    <div id="export-statistics" class="hidden"></div>
    {% else %}
    <!-- No formats available -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="p-6 text-center">
            <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center bg-red-100 rounded-full">
                <i class="ri-error-warning-line text-red-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">Няма налични формати за експорт</h3>
            <p class="text-gray-600 mb-4">
                За да използвате функцията за експорт, е необходимо да бъдат инсталирани допълнителни библиотеки.
            </p>
            <p class="text-sm text-gray-500">
                Моля, свържете се с администратора на системата за повече информация.
            </p>
        </div>
    </div>
    {% endif %}
</div>


</main>

<!-- Loading Modal -->
<div id="loading-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Генериране на експорт...</h3>
            <p class="text-sm text-gray-500">Моля, изчакайте докато файлът се подготви.</p>
        </div>
    </div>
</div>

<!-- Запазени настройки за JavaScript -->
<script>
    window.savedSettings = {{ saved_settings|json_encode|raw }};
</script>
