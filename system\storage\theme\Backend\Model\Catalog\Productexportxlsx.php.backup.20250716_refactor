<?php

namespace Theme25\Backend\Model\Catalog;

class Productexportxlsx extends \Model {

    private $languages = [];
    private $languageCount = 0;
    private $currentLanguageId = 1;
    private $dataModel;
    private $fieldHelper;

    public function __construct($registry) {
        parent::__construct($registry);

        defined('VENDORS_DIR') || define('VENDORS_DIR', DIR_SYSTEM . 'storage/vendor/');
        require_once(VENDORS_DIR . 'phpoffice/PhpSpreadsheet/autoload.php');

        $this->dataModel = $this->registry->get('exportDataModel');
        $this->fieldHelper = $this->registry->get('exportFieldHelper');
    }

    /**
     * Задава централизираната fieldHelper инстанция
     */
    public function setFieldHelper($fieldHelper) {
        if($this->fieldHelper) return;
        $this->fieldHelper = $fieldHelper;
    }

    /**
     * Задава централизираната dataModel инстанция
     */
    public function setDataModel($dataModel) {
        if(!$this->dataModel) {
            $this->dataModel = $dataModel;
        }

        // Синхронизираме language_id ако е зададен
        if (is_callable([$this, 'getLanguageId']) && is_callable([$dataModel, 'setLanguageId'])) {
            $dataModel->setLanguageId($this->getLanguageId());
        }
    }

    /**
     * Генерира XLSX файл с продукти
     */
    public function generateFile($products, $filePath, $languageId = null, $exportData = []) {
        // Проверяваме дали dataModel е зададен
        if (!$this->dataModel) {
            throw new \Exception('DataModel не е инициализиран в Productexportxlsx');
        }
        // Проверяваме дали PhpSpreadsheet е наличен
        if (!class_exists('\PhpOffice\PhpSpreadsheet\Spreadsheet')) {
            throw new \Exception('PhpSpreadsheet библиотеката не е налична. Моля, инсталирайте я чрез Composer.');
        }

        // Инициализираме езиците и текущия език
        $this->initializeLanguages($languageId);

        // Включваме debug режима ако е developer
        if (function_exists('isDeveloper') && isDeveloper()) {
            $this->fieldHelper->setDebugMode(true);
        }

        // Диагностика на експорта
        $diagnosis = $this->fieldHelper->diagnoseExport($exportData, 'xlsx');

        // Обработваме данните за експорт и определяме избраните полета
        $selectedFields = $this->processExportData($exportData);

        // Създаваме нов spreadsheet
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Products Export');

        // Записваме заглавията
        $headers = $this->getHeaders($selectedFields);
        $col = 1;
        foreach ($headers as $header) {
            $sheet->setCellValueByColumnAndRow($col, 1, $header);
            $col++;
        }

        // Стилизираме заглавията
        $headerRange = 'A1:' . \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($headers)) . '1';
        $sheet->getStyle($headerRange)->getFont()->setBold(true);
        $sheet->getStyle($headerRange)->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setARGB('FFE0E0E0');

        // Извличаме ID-тата на продуктите
        $productIds = array_column($products, 'product_id');

        // Подготвяме опциите за централизираното извличане на данни
        $options = $this->prepareDataOptions($exportData);

        // Извличаме всички данни централизирано с batch обработка
        $enrichedProducts = $this->dataModel->getProductsForExport($productIds, $options);

        // Записваме данните за продуктите
        $currentRow = 2; // Започваме от втория ред (първия заема header-а)
        foreach ($enrichedProducts as $product) {
            $rows = $this->prepareProductRows($product, $selectedFields);
            foreach ($rows as $row) {
                $col = 1;
                foreach ($row as $value) {
                    $sheet->setCellValueByColumnAndRow($col, $currentRow, $value);
                    $col++;
                }
                $currentRow++;
            }
        }

        // Прилагаме автоматично оразмеряване на колоните
        foreach (range('A', $sheet->getHighestColumn()) as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }

        // Записваме файла
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($filePath);

        // Освобождаваме паметта
        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet);
    }

    /**
     * Генерира данни за продукти за incremental експорт
     */
    public function generateProductData($products, $exportData = []) {
        // Проверяваме дали dataModel е зададен
        if (!$this->dataModel) {
            throw new \Exception('DataModel не е инициализиран в Productexportxlsx');
        }

        // Обработваме данните за експорт и определяме избраните полета
        $selectedFields = $this->processExportData($exportData);

        // Извличаме ID-тата на продуктите
        $productIds = array_column($products, 'product_id');

        // Подготвяме опциите за централизираното извличане на данни
        $options = $this->prepareDataOptions($exportData);

        // Извличаме всички данни централизирано с batch обработка
        $enrichedProducts = $this->dataModel->getProductsForExport($productIds, $options);

        // Подготвяме данните за всички продукти
        $allProductData = [];
        foreach ($enrichedProducts as $product) {
            $rows = $this->prepareProductRows($product, $selectedFields);
            foreach ($rows as $productRow) {
                $allProductData[] = $productRow;
            }
        }

        return $allProductData;
    }

    /**
     * Подготвя редовете с данни за продукт използвайки предварително извлечени данни
     */
    private function prepareProductRows($product, $selectedFields) {
        $rows = [];

        // Защита срещу липсващи зависимости
        if (!$this->dataModel) {
            throw new \Exception('DataModel не е инициализиран в prepareProductRows');
        }

        // Получаваме описанията за всички езици
        $descriptions = $product['descriptions'] ?? [];

        // Ако няма описания, създаваме празен запис за текущия език
        if (empty($descriptions)) {
            $descriptions = [$this->currentLanguageId => []];
        }

        // Създаваме ред за всеки език (или един ред ако е избран конкретен език)
        foreach ($descriptions as $languageId => $description) {
            $row = $this->prepareProductRow($product, $description, $selectedFields);
            $rows[] = $row;
        }

        return $rows;
    }

    /**
     * Подготвя един ред с данни за продукт
     */
    private function prepareProductRow($product, $description, $selectedFields) {
        $row = [];

        // Защита срещу липсващи зависимости
        if (!$this->dataModel) {
            throw new \Exception('DataModel не е инициализиран в prepareProductRow');
        }

        $availableFields = $this->fieldHelper->getFieldsForExport();

        foreach ($selectedFields as $field) {
            if (!isset($availableFields[$field])) {
                $row[] = '';
                continue;
            }

            $fieldConfig = $availableFields[$field];
            $value = '';

            // Обработваме различните типове полета
            switch ($fieldConfig['type']) {
                case 'basic':
                    $value = $product[$field] ?? '';
                    break;

                case 'description':
                    $value = $description[$field] ?? '';
                    break;

                case 'price':
                    if ($field === 'price') {
                        $value = $this->dataModel->formatPrice($product['price'] ?? 0);
                    } elseif ($field === 'promo_price') {
                        $value = $this->dataModel->formatPrice($product['promo_price'] ?? 0);
                    }
                    break;

                case 'category':
                    if ($field === 'categories') {
                        $categories = $product['categories'] ?? [];
                        $categoryPaths = [];
                        foreach ($categories as $category) {
                            $categoryPaths[] = $category['path'] ?? $category['name'] ?? '';
                        }
                        $value = implode('; ', $categoryPaths);
                    }
                    break;

                case 'option':
                    if ($field === 'options') {
                        $options = $product['options'] ?? [];
                        $optionStrings = [];
                        foreach ($options as $option) {
                            $optionStrings[] = ($option['name'] ?? '') . ': ' . ($option['value'] ?? '');
                        }
                        $value = implode('; ', $optionStrings);
                    }
                    break;

                case 'image':
                    if ($field === 'image') {
                        $value = $product['image'] ?? '';
                    } elseif ($field === 'images') {
                        $images = $product['images'] ?? [];
                        $imageUrls = [];
                        foreach ($images as $image) {
                            $imageUrls[] = $image['image'] ?? '';
                        }
                        $value = implode('; ', $imageUrls);
                    }
                    break;

                case 'seo':
                    $value = $description[$field] ?? '';
                    break;

                case 'shipping':
                    $value = $product[$field] ?? '';
                    break;

                default:
                    $value = $product[$field] ?? $description[$field] ?? '';
                    break;
            }

            // Почистваме стойността за XLSX
            $value = $this->cleanValueForXLSX($value);
            $row[] = $value;
        }

        return $row;
    }

    /**
     * Почиства стойност за XLSX експорт
     */
    private function cleanValueForXLSX($value) {
        if (is_array($value)) {
            $value = implode(', ', $value);
        }

        // Конвертираме към string
        $value = (string) $value;

        // Премахваме HTML тагове но запазваме съдържанието
        $value = strip_tags($value);

        // Декодираме HTML entities
        $value = html_entity_decode($value, ENT_QUOTES, 'UTF-8');

        // Нормализираме whitespace
        $value = preg_replace('/\s+/', ' ', $value);
        $value = trim($value);

        return $value;
    }

    // BACKUP MARKER - Останалите методи ще бъдат добавени чрез str-replace-editor
